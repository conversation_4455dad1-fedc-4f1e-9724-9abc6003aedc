// Copyright Epic Games, Inc. All Rights Reserved.

#define PROBE_FRAME_INDEX_NEVER_TRACED 0

// Must match LumenRadianceCache.cpp
#define PRIORITY_HISTOGRAM_SIZE 16
#define MAX_UPDATE_BUCKET_STRIDE 2				// [MaxBucketIndex; ProbeTraceCostFromMaxBucket]
#define PROBES_TO_UPDATE_TRACE_COST_STRIDE 2	// [ProbeTraceCost; ProbeTraceCostFromMaxBucket]

#define PROBE_TRACE_COST_DOWNSAMPLED	1
#define PROBE_TRACE_COST_NORMAL			4
#define PROBE_TRACE_COST_SUPERSAMPLED	16

uint NumProbesToTraceBudget;
float DownsampleDistanceFromCameraSq;
float SupersampleDistanceFromCameraSq;

uint GetProbeTraceCostBudget()
{
	return NumProbesToTraceBudget * PROBE_TRACE_COST_NORMAL;
}

// Note: should match GenerateProbeTraceTilesCS as closely as possible
uint GetProbeTraceCost(float3 ProbeWorldPosition)
{
	float CameraDistance = GetDistanceToCameraFromViewVector(DFHackToFloat(PrimaryView.WorldCameraOrigin) - ProbeWorldPosition);
	float DistanceFromCameraSq = CameraDistance * CameraDistance;

	if (DistanceFromCameraSq >= DownsampleDistanceFromCameraSq)
	{
		return PROBE_TRACE_COST_DOWNSAMPLED;
	}
	else if (DistanceFromCameraSq < SupersampleDistanceFromCameraSq)
	{
		return PROBE_TRACE_COST_SUPERSAMPLED;
	}

	return PROBE_TRACE_COST_NORMAL;
}