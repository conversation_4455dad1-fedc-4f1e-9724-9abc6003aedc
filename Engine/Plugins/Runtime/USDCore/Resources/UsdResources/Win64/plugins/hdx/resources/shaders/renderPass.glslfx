-- glslfx version 0.1

//
// Copyright 2019 Pixar
//
// Licensed under the terms set forth in the LICENSE.txt file available at
// https://openusd.org/license.
//

--- This is what an import might look like.
--- #import $TOOLS/hdx/shaders/renderPass.glslfx

--- --------------------------------------------------------------------------

-- layout HdxRenderPass.RenderOitOpaquePixels

[
    ["out", "vec4", "colorOut"]
]

-- glsl HdxRenderPass.RenderOitOpaquePixels

void RenderOutput(vec4 Peye, vec3 Neye, vec4 color, vec4 patchCoord)
{
    if (color.a >= 1.0) {
        colorOut = vec4(color.rgb, 1);
    } else {
        discard;
        return;
    }
}

-- layout HdxRenderPass.WriteOitLayersToBufferCommon

[
    ["in", "early_fragment_tests"],
    ["buffer readWrite", "CounterBuffer", "hdxOitCounterBuffer",
        ["atomic_int", "hdxOitCounterBuffer"]
    ]
]

-- glsl HdxRenderPass.WriteOitLayersToBufferCommon

void RenderOutputImpl(vec4 Peye, vec3 Neye, vec4 color, vec4 patchCoord)
{
    #if defined(HD_HAS_hdxOitDataBuffer)

    const int screenWidth = int(HdGet_oitScreenSize().x);
    const int screenHeight = int(HdGet_oitScreenSize().y);
    // Must match the per-pixel sample count used when creating the OIT buffers.
    // (See HdxOitResolveTask::_PrepareOitBuffers)
    const int numSamples = 8;
        
    const int dataBufferSize = screenWidth * screenHeight * numSamples;
    const int counterBufferSize = screenWidth * screenHeight + 1;

    // +1 because the counter buffer is cleared with -1, but we want the
    // first index to start at 0.
    int writeIndex = ATOMIC_ADD(hdxOitCounterBuffer[0], 1) + 1;
    
    if (writeIndex < dataBufferSize) {
        int screenIndex =
            int(gl_FragCoord.x) + int(gl_FragCoord.y) * screenWidth;

        if (screenIndex < counterBufferSize) {
            int prevIndex =
                ATOMIC_EXCHANGE(hdxOitCounterBuffer[screenIndex+1], writeIndex);
            hdxOitDataBuffer[writeIndex] = color;

            // Note that we have a choice here to either pick gl_FragCoord.z or
            // the depth value from Peye. The former is obtained by applying
            // the perspective transform and cannot be changed by a shader.
            //
            // We pick Peye here so that a shader has an opportunity to change
            // the depth of the sample inserted into the OIT list.
            //
            // This is used by volumes. However, non-volume translucent
            // geometry should never modify Peye value and call RenderOutput
            // with the Peye value from the vertex shader so that it is
            // consistent with gl_FragCoord. This is because such geometry is
            // subject to a opaque pixel render pass performing a z-test
            // against gl_FragCoord.z.
            //
            // Note there are implications of using the depth value from Peye
            // instead of gl_FragCoord.z here for the subsequent OIT resolve
            // shader: the depth sorting order needs to be flipped and the
            // OIT resolve shader cannot compare depths in the OIT list
            // against the depth buffer unless it takes the perspective
            // transform into account.
            hdxOitDepthBuffer[writeIndex] = Peye.z / Peye.w;
            hdxOitIndexBuffer[writeIndex] = prevIndex;
        }
    } else {
        // We may overrun the counter buffer integer and wrap back to 0 if
        // we have a lot of OIT samples.
        ATOMIC_ADD(hdxOitCounterBuffer[0], -1);
    }

    #endif
}

-- glsl HdxRenderPass.WriteOitLayersToBufferTranslucent

void RenderOutput(vec4 Peye, vec3 Neye, vec4 color, vec4 patchCoord)
{
    // There are two render passes for ordinary OIT geometry.
    // Fragments with alpha >= 1.0 are handled in the first (opaque)
    // render pass.
    if (color.a < 1.0 && color.a >= 0.0001) {
        RenderOutputImpl(Peye, Neye, color, patchCoord);
    }
}

-- glsl HdxRenderPass.WriteOitLayersToBufferVolume

void RenderOutput(vec4 Peye, vec3 Neye, vec4 color, vec4 patchCoord)
{
    // Unlike ordinary OIT geometry, volumes have only one render pass,
    // so insert into OIT buffers even if alpha is 1.
    if (any(greaterThan(color, vec4(0.0001)))) {
        RenderOutputImpl(Peye, Neye, color, patchCoord);
    }
}

-- layout HdxRenderPass.RenderPick

[
    ["out", "int", "primIdOut"],
    ["out", "int", "instanceIdOut"],
    ["out", "int", "elementIdOut"],
    ["out", "int", "edgeIdOut"],
    ["out", "int", "pointIdOut"],
    ["out", "vec4", "neyeOut"],
    ["buffer readWrite", "CounterBuffer", "PickBuffer", ["atomic_int", "PickBuffer"]]
]

-- glsl HdxRenderPass.RenderPick

vec4 IntToVec4(int id)
{
    return vec4(((id >>  0) & 0xff) / 255.0,
                ((id >>  8) & 0xff) / 255.0,
                ((id >> 16) & 0xff) / 255.0,
                ((id >> 24) & 0xff) / 255.0);
}

// Fwd declare necessary methods to determine the subprim id of a fragment.
FORWARD_DECL(int GetElementID()); // generated via codeGen
FORWARD_DECL(int GetPrimitiveEdgeId()); // defined in edgeId.glslfx, or generated via codeGen
FORWARD_DECL(int GetPointId()); // defined in pointId.glslfx

// Declared below.
#if defined(HD_HAS_PickBuffer)
FORWARD_DECL(void RenderDeepPicks(int primId, int instanceId, int elementId, int edgeId, int pointId));
#endif

void RenderOutput(vec4 Peye, vec3 Neye, vec4 color, vec4 patchCoord)
{
    primIdOut = HdGet_primID();

    // instanceIndex is a tuple of integers (num nested levels).
    // for picking, we store global instanceId (instanceIndex[0]) in the
    // selection framebuffer and then reconstruct the tuple in postprocess.
    instanceIdOut = GetDrawingCoord().instanceIndex[0];

    elementIdOut = GetElementID();
    edgeIdOut = GetPrimitiveEdgeId();
    pointIdOut = GetPointId();
    
    neyeOut = IntToVec4(hd_vec4_2_10_10_10_set(vec4(Neye,0)));

#if defined(HD_HAS_PickBuffer)
    RenderDeepPicks(primIdOut, instanceIdOut, elementIdOut, edgeIdOut, pointIdOut);
#endif
}

#if defined(HD_HAS_PickBuffer)
int cantor(int a, int b) {
   return (a + b + 1) * (a + b) / 2 + b;
}

// Uses cantor pairing to compute a hash value for a-b-c.
int hash3(int a, int b, int c) {
   return cantor(a, cantor(b, c));
}

bool 
compareOrSet(
    const int valueOffset, 
    const int primId, 
    const int instanceId, 
    const int partId)
{
    int primIdOffset = valueOffset;
    int instanceIdOffset = valueOffset + 1;
    int partIdOffset = valueOffset + 2;

    int primIdValue = ATOMIC_LOAD(PickBuffer[primIdOffset]);
    int instanceIdValue = ATOMIC_LOAD(PickBuffer[instanceIdOffset]);
    int partIdValue = ATOMIC_LOAD(PickBuffer[partIdOffset]);

    // Check if the slot is in use.
    // if all the values are initialized, just compare 
    if ((primIdValue != -9) && 
        (instanceIdValue != -9) && 
        (partIdValue != -9)) {
        return  (primId == primIdValue) && 
                (instanceId == instanceIdValue) && 
                (partId == partIdValue);
    } else {

        // Potentially available slot, store the values and return true if successful.
        int expected = -9;
        primIdValue = ATOMIC_COMP_SWAP(PickBuffer[primIdOffset], expected, primId);

        // Exit if the slot was unavailable.
        if (primIdValue != -9 && primIdValue != primId)
            return false;

        expected = -9;
        instanceIdValue = ATOMIC_COMP_SWAP(PickBuffer[instanceIdOffset], expected, instanceId);

        // Exit if the slot was unavailable.
        if (instanceIdValue != -9 && instanceIdValue != instanceId)
            return false;

        expected = -9;
        partIdValue = ATOMIC_COMP_SWAP(PickBuffer[partIdOffset], expected, partId);

        // Exit if the slot was unavailable.
        if (partIdValue != -9 && partIdValue != partId)
            return false;
    }
    
    return true;
}

void RenderDeepPicks(int primId, int instanceId, int elementId,
                     int edgeId, int pointId)
{
    // add the item to the pick buffer, but only unique

    // if the pick buffer is not initialized, we are done 
    if (ATOMIC_LOAD(PickBuffer[0]) == 0)
        return;
	
    // Get some constants from the Pick Buffer
    // These are initialized in: HdxPickTask::_ClearPickBuffer()
    //
    // The first 8 entries in the PickBuffer describe the buffer layout
    // [0] == The number of sub-buffers (total max hits allowed / sub-buffer size)
    //        The total max hits allowed is configurable on the pickContext (default = 32,000)
    // [1] == The max number of hits hashed and stored per sub-buffer (value = 32)
    // [2] == The TableOffset which is the first entry after this header block.  (value = 8)
    //        The Table is a collection of fields the length of the number of sub-buffers.
    //        It holds the number of current hits stored per sub-buffer. 
    //        These fields are initialized to zero and populated by this shader to track hit storage.
    // [3] == The StorageOffset which is the starting point where hits are stored.
    //        Hit results are stored in sub-buffers.
    //        Each sub-buffer holds up to 32 entries each with 3 entries per hit (primId, instanceId, partId).
    // [4] == Indicates if faces should be picked (value == 1 or 0)
    // [5] == Indicates if edges should be picked (value == 1 or 0)
    // [6] == Indicates if points should be picked (value == 1 or 0)
    // [7] == Padding
    // 
    // [TableOffset -> StorageOffset]
    //        The table of current entry counts in each sub-buffer (values initialized to 0)
    // [StorageOffset -> End of Buffer]
    //        The collection of hits as a (primId, instanceId, partId) tuple. (values initialized to -9)

    const int entrySize = 3;
    const int numSubBuffers = ATOMIC_LOAD(PickBuffer[0]);
    const int subBufferCapacity = ATOMIC_LOAD(PickBuffer[1]);
    const int tableOffset = ATOMIC_LOAD(PickBuffer[2]);
    const int storageOffset = ATOMIC_LOAD(PickBuffer[3]);
    const int partId =  ATOMIC_LOAD(PickBuffer[4]) * elementId + 
                        ATOMIC_LOAD(PickBuffer[5]) * edgeId +
                        ATOMIC_LOAD(PickBuffer[6]) * pointId;

    // compute the hash for an instance/element and assign it to a sub-buffer
    const int hashValue = hash3(primId, instanceId, partId);
    const int subBufferNumber = hashValue % numSubBuffers;
    int bufferNumber = subBufferNumber;

    // Loop through all sub buffers to find space if we fail to find any in the designated one.
    do
    {
        const int sizeOffset = tableOffset + bufferNumber;
        const int subBufferOffset = storageOffset + bufferNumber * subBufferCapacity * entrySize;

        // loop through the item's sub-buffer to see if the item is already there
        // if not, add it in atomic fashion
        int entryNumber = 0;
        do
        {
            // see if we need to add a new entry
            if (entryNumber == ATOMIC_LOAD(PickBuffer[sizeOffset])) {
                ATOMIC_COMP_SWAP(PickBuffer[sizeOffset], entryNumber, entryNumber + 1);
            }

            // if either the item equals to the current entry or we managed to 
            // initialize the entry with the item's data, we are done
            if (compareOrSet(
                    subBufferOffset + entryNumber * entrySize, 
                    primId, instanceId, partId)) {

                // We are done.
                return;
            }
        } // we exit out if we reach the capacity of the sub-buffer
        while(++entryNumber != subBufferCapacity);

        // Get the next buffer.
        bufferNumber = (bufferNumber + 1) % numSubBuffers;
    }
    while (bufferNumber != subBufferNumber);
}
#endif // HD_HAS_PickBuffer

-- layout HdxRenderPass.RenderColorAndSelection

[
    ["out", "vec4", "colorOut"],
    ["out", "float", "selectedOut"]
]

-- glsl HdxRenderPass.RenderColorAndSelection

// Note: This mixin expects color and selected color attachments in the bound
// FBO. It writes out the computed fragment color and whether it is selected
// (as a float, so, 1.0 or 0.0).

bool IsSelected(); // defined in selection.glslfx

void RenderOutput(vec4 Peye, vec3 Neye, vec4 color, vec4 patchCoord)
{
    colorOut = color;
    selectedOut = float(IsSelected());
}

-- layout HdxRenderPass.RenderColorWithOccludedSelection

[
    ["out", "vec4", "colorOut"]
]

-- glsl HdxRenderPass.RenderColorWithOccludedSelection

// Note: This mixin expects color and selected color attachments in the bound
// FBO. The alpha component of the computed fragment color is adjusted to blend
// the existing (destination) fragment color if it is selected.

bool
HasOccludedSelection(vec2 fragcoord)
{
#ifdef HD_HAS_selectedReadback
    const float isSelected = texelFetch(HdGetSampler_selectedReadback(),
                                        ivec2(fragcoord),
                                        0).x;
    return bool(isSelected);
#endif
    return false;
}

float
GetShowThroughOpacity()
{
#ifdef HD_HAS_occludedSelectionOpacity
    // Note: occludedSelectionOpacity flows in as a parameter to the selection
    // setup task (HdxSelectionTask)
    const float dstOpacity = HdGet_occludedSelectionOpacity();
    // adjust source alpha used to blend source and dst colors.
    return 1.0 - dstOpacity;
#else
    return 0.5;
#endif
}

// Input AOV textures are provided by the task.
void RenderOutput(vec4 Peye, vec3 Neye, vec4 color, vec4 patchCoord)
{
    color.rgb *= color.a;

    if (HasOccludedSelection(gl_FragCoord.xy)) {
        color.a *= GetShowThroughOpacity();
    }

    colorOut = color;
}
