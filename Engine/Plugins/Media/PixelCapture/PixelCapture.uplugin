{"FileVersion": 3, "Version": 1.0, "VersionName": "1.0", "FriendlyName": "Pixel Capture", "Description": "Framework for capturing pixel buffers in other formats while allowing for disconnected produce/consume rates.", "Category": "Graphics", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "https://docs.unrealengine.com/en-US/Platforms/PixelStreaming/index.html", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": true, "Installed": false, "Modules": [{"Name": "PixelCapture", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"], "TargetDenyList": ["Server"]}, {"Name": "PixelCaptureShaders", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"], "TargetDenyList": ["Server"]}], "Plugins": [{"Name": "MediaIOFramework", "Enabled": true}]}