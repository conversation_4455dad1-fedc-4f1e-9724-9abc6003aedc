// Copyright (c) 2024, <PERSON>. All rights reserved.


#include "ActorComponents/AttributesComponent.h"

#include "ActorComponents/StatsComponent.h"
#include "ActorComponents/ResourcesComponent.h"

#include "Entities/BaseEntity.h"
#include "Entities/Playercharacters/PlayerCharacter.h"

#include "Net/UnrealNetwork.h"
#include "Utils/DebugUtils.h"


UAttributesComponent::UAttributesComponent()
{
	PrimaryComponentTick.bCanEverTick = true;

	PopulateAttributesArray();
}


void UAttributesComponent::BeginPlay()
{
	Super::BeginPlay();
	
	OwningEntity = Cast<ABaseEntity>(GetOwner());

	// Cache the PlayerCharacter reference if the owner is a PlayerCharacter
	// This allows the Component to work with any BaseEntity while still supporting PlayerCharacter-specific functionality, such as the 'InitializeAttributesBaseValuesWithStartingValues()'
	OwningPlayerCharacter = Cast<APlayerC<PERSON>cter>(GetOwner());

	// Initialize the Base Values of Attributes with the Starting Values as defined in the DataTable only on the Server
	// Other entities (NPCs, Metinstones, Monsters) don't really have any 'Starting Values'
	if (GetOwner()->HasAuthority() && OwningPlayerCharacter)
	{
		InitializeAttributesBaseValuesWithStartingValues();
	}
}


void UAttributesComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

void UAttributesComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(UAttributesComponent, Attributes); // Replicates the Attributes variable to all clients
	DOREPLIFETIME(UAttributesComponent, AvailableAllocationPoints); // Replicates the AvailableAllocationPoints variable to all clients
}

void UAttributesComponent::OnRep_Attributes()
{
	OnAttributeUpdated.Broadcast(); // Broadcast the fact that an attribute has been updated so that any listeners can react to the change
}

/**
 * Populates the `Attributes` array with predefined attributes and initializes their impacts from a data table.
 *
 * This function clears the existing `Attributes` array, reserves memory based on the number of available
 * attributes defined in the `EAttribute` enumeration (excluding the `None` value), and adds default entries for
 * each predefined attribute. After populating the array, the function invokes `LoadAttributeImpactsFromDataTable`
 * to set up resource and stat impacts for each attribute using a predefined data table.
 *
 * @details
 * - Reserves memory for the `Attributes` array to optimize memory allocation for the predefined attributes.
 * - Adds instances of `FAttribute` for `Vitality`, `Intelligence`, `Strength`, and `Agility` to the `Attributes` array.
 * - Ensures that no residual or stale data exists in the `Attributes` array by calling `Attributes.Empty()`.
 * - Delegates the loading of attribute impacts to the `LoadAttributeImpactsFromDataTable` function.
 *
 * @note
 * - This function is intended to be called during initialization to ensure that all attributes are properly set up before gameplay begins.
 * - All enum values, except `None`, must represent valid attributes to be included in the array.
 *
 * @remarks
 * - Proper configuration of the data table used in `LoadAttributeImpactsFromDataTable` is critical for the final state of attributes.
 * - Modifications to the `EAttribute` enumeration (e.g., adding or removing attributes) might require updates to this function.
 *
 * @warning
 * - If the `LoadAttributeImpactsFromDataTable` function fails to load valid impacts for all attributes, some attributes may lack required configurations.
 */
void UAttributesComponent::PopulateAttributesArray()
{
	Attributes.Empty();

	Attributes.Reserve(StaticEnum<EAttribute>()->NumEnums() - 1); // Reserve space for all enum values except 'None'

	Attributes.Add(FAttribute{EAttribute::Vitality}); // 0 = Vitality
	Attributes.Add(FAttribute{EAttribute::Intelligence}); // 1 = Intelligence
	Attributes.Add(FAttribute{EAttribute::Strength}); // 2 = Strength
	Attributes.Add(FAttribute{EAttribute::Agility}); // 3 = Agility

	LoadAttributeImpactsFromDataTable(); // Loads the impacts of each Attribute on the Stats/Resources from the DataTable
}

/**
 * Loads the impacts of each attribute on the corresponding stats and resources from a predefined data table.
 *
 * This function retrieves all rows from the `AttributeImpactDataDataTable`, extracts the impacted resources
 * and stats for each attribute, and applies these impacts to the respective attributes stored in the component.
 * It ensures that the necessary data table is available and contains valid entries before proceeding with the loading process.
 *
 * @details
 * - The function iterates over all rows of the data table and attempts to match each row's `ResponsibleAttribute`
 *   value with the attributes stored in the component.
 * - Once a match is found, the resource and stat impacts are assigned to the corresponding attribute's `ResourceImpacts` and `StatImpacts`.
 * - Logging is performed to provide feedback on the loading process, including any failures to locate attributes in the component's array.
 *
 * @note
 * - Each row in the data table represents one attribute's responsibility for impacting specific stats and resources.
 * - The function skips any invalid or empty data rows without halting the process.
 *
 * @remarks
 * - This function is typically called during the initialization phase to ensure attribute impacts are properly loaded.
 * - Proper configuration of the `AttributeImpactDataDataTable` is critical for the expected functionality.
 *
 * @warning
 * - If `AttributeImpactDataDataTable` is invalid or empty, the function logs an error and terminates execution.
 * - Attributes that cannot be matched with entries in the data table will not have their impacts updated.
 */
void UAttributesComponent::LoadAttributeImpactsFromDataTable()
{
	if (!AttributeImpactDataDataTable)
	{
		AVALORE_ERROR_LOG(TEXT("Invalid AttributeImpactDataDataTable!"));
		return;
	}

	TArray<FAttributeImpactsData*> AllRows;
	AttributeImpactDataDataTable->GetAllRows<FAttributeImpactsData>(TEXT("LoadAttributeImpactsFromDataTable"), AllRows);

	if (AllRows.Num() == 0)
	{
		AVALORE_ERROR_LOG(TEXT("AttributeImpactDataDataTable is empty!"));
		return;
	}

	for (const FAttributeImpactsData* Row : AllRows)
	{
		if (!Row)
			continue;

		FAttribute* TargetAttribute = FindAttributeByType(Row->ResponsibleAttribute);
		if (!TargetAttribute)
		{
			AVALORE_WARNING_LOG(TEXT("Could not find Attribute %s in Attributes array!"), *UEnum::GetValueAsString(Row->ResponsibleAttribute));
			continue;
		}

		TargetAttribute->ResourceImpacts = Row->ImpactedResources;
		TargetAttribute->StatImpacts = Row->ImpactedStats;

		AVALORE_STANDARD_LOG(TEXT("Loaded impacts for Attribute %s: %d resource impacts, %d stat impacts"), *UEnum::GetValueAsString(Row->ResponsibleAttribute), TargetAttribute->ResourceImpacts.Num(),
			TargetAttribute->StatImpacts.Num());
	}
}

/**
 * Initializes the base values of attributes with the starting values as defined in a data table.
 *
 * This function retrieves starting attribute values based on the owning player character's variant
 * and assigns these values to the respective base components of the attributes (e.g., Vitality, Intelligence, Strength, Agility).
 * It validates that the required data table and owning player character are available, and performs error logging if any
 * validation fails.
 *
 * @note
 * - The function converts the player character's variant enum to a row name for accessing the data table.
 * - If the required row is missing from the data table or invalid, an error is logged and no changes are made.
 * - Successful initialization logs confirmation with the relevant variant information.
 *
 * @remarks
 * - This function is intended to be called during the setup phase of the player character to ensure proper initialization.
 * - The `SetAttributeValue` method is used to update the base component of each attribute, propagating any necessary recalculations or updates.
 * - Proper initialization ensures attributes are set correctly based on the defined starting values for the player's variant.
 *
 * @warning
 * - Ensure `StartingAttributesDataTable` is properly populated and linked before calling this function.
 * - The owning player character must have a valid variant value, or initialization will not proceed.
 */
void UAttributesComponent::InitializeAttributesBaseValuesWithStartingValues()
{
	// This function is specifically designed for PlayerCharacters, so we need to validate that the OwningPlayerCharacter is valid
	if (!OwningPlayerCharacter)
	{
		AVALORE_ERROR_LOG(TEXT("InitializeAttributesBaseValuesWithStartingValues called on non-PlayerCharacter entity!"));
		return;
	}
	
	// Validate StartingAttributesDataTable
	if (!StartingAttributesDataTable)
	{
		AVALORE_ERROR_LOG(TEXT("StartingAttributesDataTable is null! Cannot initialize attribute Base Values!"));
		return;
	}

	// Convert Enum Variant to FName to use it as a row name for the DataTable
	FString VariantString = UEnum::GetValueAsString(OwningPlayerCharacter->Variant);
	FName RowName = FName(*VariantString);

	// Find the row in the DataTable
	FStartingAttributes* StartingAttributesRow = StartingAttributesDataTable->FindRow<FStartingAttributes>(RowName, TEXT("InitializeAttributesBaseValuesWithStartingValues"));
	if (!StartingAttributesRow)
	{
		AVALORE_ERROR_LOG(TEXT("Failed to find starting attributes row for Variant: %s"), *VariantString);
		return;
	}

	// Set the Base Values for each Attribute
	SetAttributeValue(EAttribute::Vitality, EAttributeComponent::Base, StartingAttributesRow->Vitality);
	SetAttributeValue(EAttribute::Intelligence, EAttributeComponent::Base, StartingAttributesRow->Intelligence);
	SetAttributeValue(EAttribute::Strength, EAttributeComponent::Base, StartingAttributesRow->Strength);
	SetAttributeValue(EAttribute::Agility, EAttributeComponent::Base, StartingAttributesRow->Agility);

	AVALORE_STANDARD_LOG(TEXT("Successfully initialize Base Attribute values for PlayerCharacter Variant %s"), *VariantString);
}

/**
 * Sets the value of a specific component of an attribute.
 *
 * This function modifies the value of one of the components (e.g., Base, Allocated, or Other)
 * of a given attribute, triggers any related updates, and ensures all necessary validations
 * and recalculations are performed. It is only executed on the authority (server) side and
 * updates are replicated to clients.
 *
 * @param Attribute The specific attribute (e.g., Vitality, Strength) whose component value is being set.
 * @param Component The specific component of the attribute to modify (e.g., Base, Allocated, Other).
 * @param Value The new value to set for the specified attribute's component.
 *
 * @note
 * - The function checks for authority before making any changes to the attribute.
 * - All indices and types are validated to prevent crashes or unexpected behavior due to
 *   invalid array accesses or type mismatches.
 * - Changes to attributes automatically trigger replication to clients, and the `OnRep_Attributes`
 *   function is called on the clients upon receiving the update.
 *
 * @remarks
 * - This function calls `RecalculateAttribution()` to recalculate stats and resources affected by
 *   the updated attribute value. For example, a change in the Vitality attribute may require recalculating
 *   the maximum health of a player.
 * - The `OnAttributeUpdated` event is broadcast to signal any systems or UI components
 *   that might rely on attribute changes.
 */
void UAttributesComponent::SetAttributeValue(EAttribute Attribute, EAttributeComponent Component, int32 Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	// Convert the Enum to an Index
	// The Attributes array is indexed by the EAttribute enum (e.g., Vitality = 0, Strength = 1, etc.) so cast the Enum to int32 to use it as an array index
	int32 AttributeIndex = static_cast<int32>(Attribute);
	
	// Validate Array Bounds
	// Check if the calculated index is within the valid range of our Attributes array. This prevents crashes from accessing invalid memory locations.
	if (!Attributes.IsValidIndex(AttributeIndex))
		return;

	// Verify Attribute Type Consistency
	// Double-check that the attribute stored at this index matches what we expect. This is a safety measure in case the array gets corrupted or misordered.
	if (Attributes[AttributeIndex].Attribute != Attribute)
		return;

	// Update the Attribute Component Value using individual variables instead of Components array
	// Set the new value in the specific component of the attribute using the new individual variable approach
	switch (Component)
	{
	case EAttributeComponent::Base:
		Attributes[AttributeIndex].Base = Value;
		break;
	case EAttributeComponent::Allocated:
		Attributes[AttributeIndex].Allocated = Value;
		break;
	case EAttributeComponent::Other:
		Attributes[AttributeIndex].Other = Value;
		break;
	}

	// Broadcast the Attribute Update Event
	// Notify other components or systems that an attribute has been updated. This could trigger UI updates, stat recalculations, etc.
	OnAttributeUpdated.Broadcast();

	// Trigger Recalculation of Stats Attribution
	// Stats are Resources are effected by Attributes values, so we need to recalculate the attribution of all stats when an attribute's value changes
	// For example, Vitality affects Health, so when Vitality changes, we need to recalculate the Health stat
	RecalculateAttribution();

	// Note that Attributes array is marked as Replicated, so this change will automatically be sent to all Clients from the Server.
	// When Clients receive it, the OnRep_Attributes() function will be called, which will broadcast the OnAttributeUpdated event.
}

/**
 * Retrieves the value of a specified attribute component or the full attribute's total value.
 *
 * This function retrieves the value of an attribute's specific component, such as Base, Allocated, or Other.
 * Alternatively, if the total value of the attribute (sum of all its components) is required, it can bypass
 * individual component extraction based on the 'bIgnoreComponentAndReturnTotal' parameter.
 *
 * @param Attribute The specific attribute (e.g., Vitality, Strength) whose value is being retrieved.
 * @param Component The specific component of the attribute (e.g., Base, Allocated, or Other) to retrieve.
 * @param bIgnoreComponentAndReturnTotal If true, returns the total value of the attribute (sum of all components)
 * instead of a specific component value.
 *
 * @return The value of the requested attribute component, or the total value of the attribute if
 * 'bIgnoreComponentAndReturnTotal' is true. Returns 0 if the input parameters are invalid or out of bounds.
 */
int32 UAttributesComponent::GetAttributeValue(EAttribute Attribute, EAttributeComponent Component, bool bIgnoreComponentAndReturnTotal) const
{
	// Convert Enum to Array Index
	// The Attributes array is indexed by the EAttribute enum (e.g., Vitality = 0, Strength = 1, etc.) so cast the Enum to int32 to use it as an array index
	int32 AttributeIndex = static_cast<int32>(Attribute);

	// Validate Array Bounds
	// Check if the calculated index is within the valid range of our Attributes array. This prevents crashes from accessing invalid memory locations.
	if (!Attributes.IsValidIndex(AttributeIndex))
		return 0;

	// Verify Attribute Type Consistency
	// Double-check that the attribute stored at this index matches what we expect. This is a safety measure in case the array gets corrupted or misordered.
	if (Attributes[AttributeIndex].Attribute != Attribute)
		return 0;

	// Check if we should return total value instead of specific component only
	// If 'bIgnoreComponentAndReturnTotal' is true, we should return the total value of the attribute (sum of all its components, aka 'Base' + 'Allocated' + 'Other')
	if (bIgnoreComponentAndReturnTotal)
	{
		// Calculate total by summing all three components using individual variables
		int32 Total = Attributes[AttributeIndex].Base + Attributes[AttributeIndex].Allocated + Attributes[AttributeIndex].Other;
		return Total;
	}

	// Return Specific Component Value using individual variables instead of Components array
	switch (Component)
	{
	case EAttributeComponent::Base:
		return Attributes[AttributeIndex].Base;
	case EAttributeComponent::Allocated:
		return Attributes[AttributeIndex].Allocated;
	case EAttributeComponent::Other:
		return Attributes[AttributeIndex].Other;
	default:
		return 0; // Return 0 as safe fallback for invalid component
	}
}


void UAttributesComponent::ServerSetAttributeValue_Implementation(EAttribute Attribute, EAttributeComponent Component, int32 Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	SetAttributeValue(Attribute, Component, Value);
}

/**
 * Increases the value of a specific component of an attribute.
 *
 * This function retrieves the current value of the specified attribute's component,
 * adds the provided increase value, and then updates the attribute's component value
 * accordingly while ensuring all necessary validations, recalculations, and replications are performed.
 *
 * @param Attribute The specific attribute (e.g., Vitality, Strength) whose component value is to be increased.
 * @param Component The specific component of the attribute to modify (e.g., Base, Allocated, Other).
 * @param Value The amount to increase the value of the specified attribute's component.
 *
 * @note
 * - Authority checks are performed to ensure this function executes only on the server.
 * - Changes to the attribute's component value automatically trigger replication to clients,
 *   along with necessary dependent updates such as recalculation of related derived stats.
 * - `GetAttributeValue` and `SetAttributeValue` functions are internally used to retrieve
 *   and update the values of the specified attribute's component.
 *
 * @remarks
 * - Using this method ensures proper validation and broadcasting of changes through the built-in attribute system.
 * - Dependent systems are notified of attribute changes through appropriate replication mechanisms and events.
 */
void UAttributesComponent::IncreaseAttributeValue(EAttribute Attribute, EAttributeComponent Component, int32 Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	// Get Current Component Value
	// First, retrieve the current value of the specific attribute component. We use false for bIgnoreComponentAndReturnTotal to get the specific component value only
	int32 CurrentValue = GetAttributeValue(Attribute, Component, false);

	// Calculate New Value
	// Add the increase amount to the current value to get the new total
	int32 NewValue = CurrentValue + Value;

	// Set the New Value
	// Use SetAttributeValue() to apply the new total value, which handles all validation, broadcasting, and replication
	SetAttributeValue(Attribute, Component, NewValue);
}

void UAttributesComponent::ServerIncreaseAttributeValue_Implementation(EAttribute Attribute, EAttributeComponent Component, int32 Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	IncreaseAttributeValue(Attribute, Component, Value);
}

/**
 * Recalculates the attribution of all attributes to their respective stats and resources.
 *
 * This function iterates through all attributes in the component and applies their effects
 * to the corresponding stats and resources based on each attribute's impact target data.
 * The attribution system allows attributes to dynamically affect multiple stats and resources
 * with configurable multipliers (VPA - Value Per Attribute point).
 *
 * The function performs the following operations:
 * 1. Loops through each attribute in the Attributes array
 * 2. Calculates the total value of each attribute (sum of Base + Allocated + Other components)
 * 3. For each attribute, processes all its impact targets defined in ImpactTargetsData
 * 4. Calculates the attributed value using the formula: AttributeTotal * VPA
 * 5. Updates the 'Attributed' component of the target stat or resource with the calculated value
 *
 * @note
 * - This function only executes on the server (authority check) to ensure network consistency.
 * - The function requires valid StatsComponent and ResourcesComponent references from the owning entity.
 * - All calculations affect only the 'Attributed' component of stats and resources, leaving
 *   other components (Base, Itemized, Other) unchanged.
 * - The VPA (Value Per Attribute point) multiplier allows for flexible scaling of attribute effects.
 *
 * @remarks
 * - This function is typically called when attribute values change to ensure dependent stats
 *   and resources reflect the updated attribute values.
 * - The impact target system supports both EAttributeImpactTarget::Resource and 
 *   EAttributeImpactTarget::Stat, allowing attributes to affect either type of target.
 * - Each attribute can have multiple impact targets, enabling complex attribute interactions
 *   (e.g., Vitality affecting both Health stat and Health resource).
 *
 * @warning
 * - Ensure that ImpactTargetsData is properly configured in the attribute setup, as invalid
 *   enum values in the switch statement will be ignored without error logging.
 * - The function assumes that all referenced stats and resources exist in their respective components.
 *
 * Example:
 * If Vitality has a total value of 50 and an impact target with VPA of 10.0 targeting Health resource,
 * the Health resource's Attributed component will be set to 500.0 (50 * 10.0).
 */
void UAttributesComponent::RecalculateAttribution()
{
	if (!GetOwner()->HasAuthority())
		return;

	// Get references to the StatsComponent and ResourcesComponent from the OwningEntity
	// These components are required to apply the calculated attribution values
	UStatsComponent* StatsComponent = OwningEntity->StatsComponent;
	UResourcesComponent* ResourcesComponent = OwningEntity->ResourcesComponent;

	// Validate StatsComponent and ResourcesComponent exist
	// If either component is missing, we cannot proceed with attribution
	if (!StatsComponent || !ResourcesComponent)
		return;

	// Iterate through each attribute in the Attributes array
	// Each Attribute can potentially affect multiple stats and/or resources
	for (const FAttribute& Attribute : Attributes)
	{
		// Calculate the total value of the current Attribute by summing all its components (Base, Allocated, Other)
		// Use use 'true' for bIgnoreComponentAndReturnTotal to get the total value of the Attribute
		int32 AttributeTotalValue = GetAttributeValue(Attribute.Attribute, EAttributeComponent::Other, true);

		// Process Resource Impacts using the new separate array
		for (const FAttributeResourceImpactData& ResourceImpact : Attribute.ResourceImpacts)
		{
			// Calculate the final attributed value by using the VPA multiplier
			// Formula: AttributeTotalValue * VPA = Final Attributed Value
			// Example: Vitality (total 50) with VPA of 10.0 targeting Health resource results in 500.0 added to Health's Attributed component
			float CalculatedValue = AttributeTotalValue * ResourceImpact.VPA;

			// Apply the calculated value to the 'Attributed' component of the target 'Resource'
			// This affects resources like Health, Mana, Stamina, etc.
			ResourcesComponent->SetResourceValue(ResourceImpact.Resource, EResourceComponent::Attributed, CalculatedValue);
		}

		// Process Stat Impacts using the new separate array
		for (const FAttributeStatImpactData& StatImpact : Attribute.StatImpacts)
		{
			// Calculate the final attributed value by using the VPA multiplier
			// Formula: AttributeTotalValue * VPA = Final Attributed Value
			// Example: Intelligence (total 30) with VPA of 2.0 targeting AbilityPower stat results in 60.0 added to AbilityPower's Attributed component
			float CalculatedValue = AttributeTotalValue * StatImpact.VPA;

			// Apply the calculated value to the 'Attributed' component of the target 'Stat'
			// This affects stats like Attack Speed, Armor, etc.
			StatsComponent->SetStatValue(StatImpact.Stat, EStatComponent::Attributed, CalculatedValue);
		}
	}
}

FAttribute* UAttributesComponent::FindAttributeByType(EAttribute AttributeType)
{
	for (FAttribute& Attribute : Attributes)
	{
		if (Attribute.Attribute == AttributeType)
		{
			return &Attribute;
		}
	}
	return nullptr;
}
