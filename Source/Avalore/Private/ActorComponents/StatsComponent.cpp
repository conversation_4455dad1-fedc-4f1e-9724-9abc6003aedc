// Copyright (c) 2024, <PERSON>. All rights reserved.


#include "ActorComponents/StatsComponent.h"

#include "Entities/BaseEntity.h"
#include "Net/UnrealNetwork.h"


UStatsComponent::UStatsComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
}

void UStatsComponent::BeginPlay()
{
	Super::BeginPlay();

	OwningEntity = Cast<ABaseEntity>(GetOwner());
}


void UStatsComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

void UStatsComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(UStatsComponent, Stats); // Replicates the Stats variable to all Clients
}

void UStatsComponent::OnRep_Stats()
{
	OnStatUpdated.Broadcast(); // Broadcast the fact that a stat has been updated so that any listeners can react to the change
}

void UStatsComponent::SetStatValue(EStat Stat, EStatComponent Component, float Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	// Convert Enum to Array Index
	int32 StatIndex = static_cast<int32>(Stat);

	// Validate Array Bounds
	if (!Stats.IsValidIndex(StatIndex))
	{
		return;
	}

	// Verify Stat Type Consistency
	if (Stats[StatIndex].Stat != Stat)
		return;

	// Set the Stat Component Value using individual variables instead of Components array
	switch (Component)
	{
	case EStatComponent::Base:
		Stats[StatIndex].Base = Value;
		break;
	case EStatComponent::Attributed:
		Stats[StatIndex].Attributed = Value;
		break;
	case EStatComponent::Itemized:
		Stats[StatIndex].Itemized = Value;
		break;
	case EStatComponent::Other:
		Stats[StatIndex].Other = Value;
		break;
	}

	// Broadcast the Stat Update Event
	OnStatUpdated.Broadcast();
}

float UStatsComponent::GetStatValue(EStat Stat, EStatComponent Component, bool bIgnoreComponentAndReturnTotal) const
{
	// Convert Stat Enum to Array Index
	int32 StatIndex = static_cast<int32>(Stat);

	// Validate Array Bounds
	if (!Stats.IsValidIndex(StatIndex))
		return 0.0f;

	// Verify Stat Type Consistency
	if (Stats[StatIndex].Stat != Stat)
		return 0.0f;

	// Check if we should return total value instead of specific component only
	if (bIgnoreComponentAndReturnTotal)
	{
		// Calculate total by summing all components using individual variables
		float Total = Stats[StatIndex].Base + Stats[StatIndex].Attributed + Stats[StatIndex].Itemized + Stats[StatIndex].Other;
		return Total;
	}

	// Return Specific Component Value using individual variables instead of Components array
	switch (Component)
	{
	case EStatComponent::Base:
		return Stats[StatIndex].Base;
	case EStatComponent::Attributed:
		return Stats[StatIndex].Attributed;
	case EStatComponent::Itemized:
		return Stats[StatIndex].Itemized;
	case EStatComponent::Other:
		return Stats[StatIndex].Other;
	default:
		return 0.0f; // Return 0.0f as safe fallback for invalid component
	}
}

void UStatsComponent::IncreaseStatValue(EStat Stat, EStatComponent Component, float Value)
{
	if (!GetOwner()->HasAuthority())
		return;

	// Get Current Value
	float CurrentValue = GetStatValue(Stat, Component, false);

	// Calculate New Value
	float NewValue = CurrentValue + Value;

	// Set the New Value
	SetStatValue(Stat, Component, NewValue);
}

/**
 * Initializes the Stats array with all available stats.
 * This function populates the array with all stats that entities can have.
 */
void UStatsComponent::InitializeStatsArray()
{
	Stats.Empty();

	// Health Regeneration Stat
	FStat HealthRegenStat;
	HealthRegenStat.Stat = EStat::HealthRegeneration;
	Stats.Add(HealthRegenStat);

	// Mana Regeneration Stat
	FStat ManaRegenStat;
	ManaRegenStat.Stat = EStat::ManaRegeneration;
	Stats.Add(ManaRegenStat);

	// Stamina Regeneration Stat
	FStat StaminaRegenStat;
	StaminaRegenStat.Stat = EStat::StaminaRegeneration;
	Stats.Add(StaminaRegenStat);

	// Attack Value Stat
	FStat AttackValueStat;
	AttackValueStat.Stat = EStat::AttackValue;
	Stats.Add(AttackValueStat);

	// Ability Power Stat
	FStat AbilityPowerStat;
	AbilityPowerStat.Stat = EStat::AbilityPower;
	Stats.Add(AbilityPowerStat);

	// Attack Speed Stat
	FStat AttackSpeedStat;
	AttackSpeedStat.Stat = EStat::AttackSpeed;
	Stats.Add(AttackSpeedStat);

	// Armor Stat
	FStat ArmorStat;
	ArmorStat.Stat = EStat::Armor;
	Stats.Add(ArmorStat);

	// Spellguard Stat
	FStat SpellguardStat;
	SpellguardStat.Stat = EStat::Spellguard;
	Stats.Add(SpellguardStat);

	// Movement Speed Stat
	FStat MovementSpeedStat;
	MovementSpeedStat.Stat = EStat::MovementSpeed;
	Stats.Add(MovementSpeedStat);

	// Attack Range Stat
	FStat AttackRangeStat;
	AttackRangeStat.Stat = EStat::AttackRange;
	Stats.Add(AttackRangeStat);

	// Ability Haste Stat
	FStat AbilityHasteStat;
	AbilityHasteStat.Stat = EStat::AbilityHaste;
	Stats.Add(AbilityHasteStat);

	// Leech Stat
	FStat LeechStat;
	LeechStat.Stat = EStat::Leech;
	Stats.Add(LeechStat);

	// Critical Chance Stat
	FStat CriticalChanceStat;
	CriticalChanceStat.Stat = EStat::CriticalChance;
	Stats.Add(CriticalChanceStat);

	// Critical Damage Stat
	FStat CriticalDamageStat;
	CriticalDamageStat.Stat = EStat::CriticalDamage;
	Stats.Add(CriticalDamageStat);
}
