// Copyright (c) 2024, <PERSON>. All rights reserved.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "Structs/AvaloreStructs.h"
#include "Structs/AvaloreEnums.h"

#include "AttributesComponent.generated.h"

class ABaseEntity;
class APlayerCharacter;

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class AVALORE_API UAttributesComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	// Sets default values for this component's properties
	UAttributesComponent();

protected:
	// Called when the game starts
	virtual void BeginPlay() override;

public:
	// Called every frame
	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

protected:
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Setup Required|Data Tables")
	TObjectPtr<UDataTable> StartingAttributesDataTable; // The data table containing the starting values for each Attribute of each PlayerCharacter variant; needs to be filled in the Editor
	
protected:
	UPROPERTY()
	TObjectPtr<ABaseEntity> OwningEntity; // The entity that owns this AttributesComponent
	
	UPROPERTY()
	TObjectPtr<APlayerCharacter> OwningPlayerCharacter; // This gets filled only if the OwningEntity is a PlayerCharacter

public:
	UPROPERTY(EditDefaultsOnly, Category = "Setup Required|Data Tables")
	TObjectPtr<UDataTable> AttributeImpactDataDataTable; // The data table containing the impacts of each Attribute on the Stats and Resources; needs to be filled in the Editor
	
	UPROPERTY(VisibleDefaultsOnly, BlueprintReadOnly, ReplicatedUsing = OnRep_Attributes, Category = "Attributes")
	TArray<FAttribute> Attributes;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Replicated, Category = "Attributes")
	int32 AvailableAllocationPoints = 0; // The number of points available for allocation provided to the player at each level up

public:
	UFUNCTION()
	void OnRep_Attributes(); // Replication function created due to ReplicatedUsing = OnRep_Attributes

	// Event Dispatcher for when an attribute's value changes.
	DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnAttributeUpdated);
	UPROPERTY(BlueprintAssignable, Category = "Event Dispatchers")
	FOnAttributeUpdated OnAttributeUpdated;

protected:
	void PopulateAttributesArray();
	
public:
	// Each PlayerCharacter has a set of starting values for each attribute upon creation of their character, thus we need to initialize attributes' base values with these starting values
	void InitializeAttributesBaseValuesWithStartingValues();

	void SetAttributeValue(EAttribute Attribute, EAttributeComponent Component, int32 Value); // Sets the value of the attribute's component to the given value

	int32 GetAttributeValue(EAttribute Attribute, EAttributeComponent Component, bool bIgnoreComponentAndReturnTotal) const; // Returns the value of the attribute's component (or the total value if 'bIgnoreComponentAndReturnTotal' is true)

	void IncreaseAttributeValue(EAttribute Attribute, EAttributeComponent Component, int32 Value); // Increases the value of the attribute's component by the given value

	// Stats (in the 'StatsComponent') are affected by values in attributes, so we need to recalculate the attribution of all stats when an attribute's value changes
	void RecalculateAttribution();

public: // RPCs //
	UFUNCTION(Server, Reliable, BlueprintCallable)
	void ServerSetAttributeValue(EAttribute Attribute, EAttributeComponent Component, int32 Value);

	UFUNCTION(Server, Reliable, BlueprintCallable)
	void ServerIncreaseAttributeValue(EAttribute Attribute, EAttributeComponent Component, int32 Value);
};
