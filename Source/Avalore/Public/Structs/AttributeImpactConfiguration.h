// Copyright (c) 2024, <PERSON>. All rights reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataTable.h"
#include "Structs/AvaloreEnums.h"
#include "Structs/AvaloreStructs.h"

#include "AttributeImpactConfiguration.generated.h"

/**
 * Data table row structure for configuring attribute impact targets.
 * This allows designers to easily configure which stats/resources each attribute affects
 * and by how much (VPA - Value Per Attribute point).
 */
USTRUCT(BlueprintType)
struct FAttributeImpactConfigurationRow : public FTableRowBase
{
	GENERATED_BODY()

	// The attribute this configuration applies to
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attribute")
	EAttribute Attribute = EAttribute::None;

	// NEW: Separate arrays for different impact types
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impact Targets")
	TArray<FAttributeResourceImpactData> ResourceImpacts;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impact Targets")
	TArray<FAttributeStatImpactData> StatImpacts;

	// DEPRECATED: Keep for backward compatibility
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Impact Targets")
	TArray<FAttributeImpactTargetData> ImpactTargets;
};

/**
 * Helper class for managing attribute impact configurations.
 * This class provides utilities for loading and applying attribute impact data
 * from data tables to attribute arrays.
 */
UCLASS(BlueprintType)
class AVALORE_API UAttributeImpactConfigurationHelper : public UObject
{
	GENERATED_BODY()

public:
	/**
	 * Loads attribute impact configuration from a data table and applies it to an attributes array.
	 * @param AttributesArray The array of attributes to configure
	 * @param ConfigurationDataTable The data table containing impact configuration
	 * @return True if configuration was successfully applied, false otherwise
	 */
	UFUNCTION(BlueprintCallable, Category = "Attribute Configuration")
	static bool ApplyImpactConfigurationFromDataTable(TArray<FAttribute>& AttributesArray, UDataTable* ConfigurationDataTable);

	/**
	 * Creates default impact configuration for common attribute relationships.
	 * This provides sensible defaults that can be used as a starting point.
	 * @param AttributesArray The array of attributes to configure with defaults
	 */
	UFUNCTION(BlueprintCallable, Category = "Attribute Configuration")
	static void ApplyDefaultImpactConfiguration(TArray<FAttribute>& AttributesArray);

private:
	/**
	 * Helper function to find an attribute in the array by its enum value.
	 * @param AttributesArray The array to search
	 * @param AttributeType The attribute type to find
	 * @return Pointer to the found attribute, or nullptr if not found
	 */
	static FAttribute* FindAttributeByType(TArray<FAttribute>& AttributesArray, EAttribute AttributeType);
};
